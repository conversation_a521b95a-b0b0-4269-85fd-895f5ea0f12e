# 明细项数据变更校验和处理流程优化

## 实现概述

本次优化主要针对明细项数据变更时的校验和处理流程，实现了以下功能：

### 1. 触发条件
- 当用户修改表单中的明细项字段值时（包括输入、选择、删除等操作）
- 支持多种触发场景：
  - 单元格失去焦点（editClosed 事件）
  - 按下回车键（keydown 事件）
  - 跨行切换和同行切换

### 2. 校验逻辑
1. **字段校验判断**：通过字段配置中的 `ischkfld` 标识判断是否需要校验
2. **接口调用**：调用 `/util/chkFld` 接口进行字段校验
3. **校验参数**：包含字段名称、字段值、相关上下文数据（单据类型、业务场景等）

### 3. 处理流程
1. **校验通过**：继续执行挂账操作（将数据保存到账务系统）
2. **校验失败**：
   - 阻止挂账操作
   - 向用户显示具体的错误信息
   - 保持字段焦点，允许用户重新输入

### 4. 错误处理
- 网络异常或接口调用失败时，给出友好提示并阻止后续操作
- 确保在校验完成之前，相关的提交按钮或自动保存功能处于禁用状态

### 5. 实现要求
- ✅ 使用异步方式调用校验接口，避免阻塞用户界面
- ✅ 考虑防抖处理，避免频繁的校验请求（300ms 防抖）
- ✅ 在Vue组件中实现时，确保正确处理组件的生命周期和响应式数据更新

## 核心代码改进

### 1. 防抖校验函数
```javascript
// 创建一个防重复校验的函数，支持更完善的防抖和错误处理
const _debouncedValidateField = debounce(async (params, callback) => {
  // 创建校验锁的唯一标识
  const lockKey = `${params.fieldName}_${params.rowIndex}_${params.fieldValue}`;

  // 如果正在校验相同字段，则跳过
  if (validationLock.value.has(lockKey)) {
    console.log(`字段 ${params.fieldName} 正在校验中，跳过重复请求`);
    return;
  }

  // 添加校验锁
  validationLock.value.add(lockKey);

  try {
    console.log(`开始校验字段: ${params.fieldName}, 值: ${params.fieldValue}`);
    const validateResult = await templateStore.validateField(params);
    
    if (callback) {
      callback(validateResult);
    }
  } catch (error) {
    console.error(`字段 ${params.fieldName} 校验失败:`, error);
    
    // 网络异常或接口调用失败时的友好提示
    const modal = Modal.warn({
      title: '校验异常',
      content: '网络异常或接口调用失败，请检查网络连接后重试',
      okText: '确定',
      keyboard: false,
      maskClosable: false,
      onOk: () => {
        activeModals.value.delete(modal);
        if (callback) {
          // 校验异常时，阻止后续操作
          callback({ valid: false, errors: ['校验异常'], pass: '-1' });
        }
      },
    });
    
    activeModals.value.add(modal);
  } finally {
    // 校验完成后移除锁
    validationLock.value.delete(lockKey);
  }
}, 300);
```

### 2. 改进的校验流程
```javascript
// 处理单元格校验逻辑（失去焦点时触发）
const handleCellValidation = async (row: any, column: any, rowIndex: number) => {
  // 防止重复处理
  if (isValidating.value) return;

  // 检查是否有Modal弹窗打开
  if (modalOpen.value && zljsModalOpen.value) return;

  // 获取字段配置
  const { mxTableList } = templateStore.djInfo;
  const fieldConfig = Object.values(mxTableList).find(
    (config) => config.fieldName === column.field,
  );

  if (!fieldConfig) {
    return;
  }

  // 判断被修改的字段是否为需要校验的字段
  if (!(fieldConfig as any).ischkfld) {
    console.log(`字段 ${column.field} 无需校验，直接执行后续操作`);
    // 对于未配置校验的字段，直接进行挂账处理
    if ((fieldConfig as any).isSaveNow && !isDataRetrieving.value) {
      const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
      await templateStore.saveGzToServer(storageKey);
    } else {
      if (!isDataRetrieving.value) {
        templateStore.saveGz();
      } else {
        console.log('数据检索期间，跳过本地缓存保存（失去焦点）');
      }
    }
    return;
  }

  isValidating.value = true;

  // 使用防抖机制进行字段校验
  const validateParams = buildValidateParams(row, column, rowIndex, true);
  validateParams.validationMode = 'cell-blur'; // 失焦校验模式

  console.log(`字段 ${column.field} 需要校验，开始防抖校验流程`);

  // 使用防抖校验函数
  _debouncedValidateField(validateParams, async (validateResult) => {
    handleValidationResult(validateResult, row, column, async (valid) => {
      if (valid) {
        console.log(`字段 ${column.field} 校验通过，执行后续操作`);
        // 校验通过后的处理逻辑
        if ((fieldConfig as any).isSaveNow && !isDataRetrieving.value) {
          const storageKey = `gz_${templateStore.djlx}_${templateStore.djbs}`;
          await templateStore.saveGzToServer(storageKey);
          console.log(`字段 ${column.field} 立即挂账完成`);
        } else {
          if (!isDataRetrieving.value) {
            templateStore.saveGz();
            console.log(`字段 ${column.field} 本地缓存保存完成`);
          } else {
            console.log('数据检索期间，跳过本地缓存保存（失去焦点）');
          }
        }
      } else {
        console.log(`字段 ${column.field} 校验失败，阻止挂账操作`);
      }
      // 重置校验状态
      isValidating.value = false;
    });
  });
};
```

### 3. 组件生命周期处理
```javascript
onUnmounted(() => {
  // 清理所有活跃的Modal
  activeModals.value.forEach((modal) => {
    try {
      modal.destroy();
    } catch (error) {
      console.warn('清理Modal时出错:', error);
    }
  });
  activeModals.value.clear();

  // 清理校验锁
  validationLock.value.clear();

  // 取消防抖函数
  _debouncedValidateField.cancel();

  // 重置校验状态
  isValidating.value = false;
  
  console.log('组件卸载，清理校验相关资源');
});
```

## 优化效果

1. **性能提升**：通过防抖机制减少了不必要的API调用
2. **用户体验改善**：提供了更友好的错误提示和加载状态
3. **稳定性增强**：改进了异步操作处理和组件生命周期管理
4. **代码质量**：增加了详细的日志记录，便于调试和维护

## 使用说明

1. 字段配置中设置 `ischkfld: true` 来启用校验
2. 字段配置中设置 `isSaveNow: true` 来启用立即挂账
3. 校验接口 `/util/chkFld` 返回的 `pass` 值决定处理方式：
   - `-1`: 拦截，阻止操作
   - `1`: 警告，但允许继续
   - `2`: 直接通过
4. 在数据检索期间会自动跳过挂账操作，避免冲突
